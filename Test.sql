WITH 
HistoryAndLogs AS (
  SELECT DISTINCT
    {BusinessOperationExecutionLog}.[Id] AS ID, 
    {BusinessOperationExecutionLog}.[Instant] AS Instant, 
    {BusinessOperationExecutionLog}.[RootId] AS RootId, 
    {BusinessOperationExecutionLog}.[CompletedDateTime] AS CompletedDateTime, 
    {BusinessOperationExecutionLog}.[Success] AS Success, 
    null AS FeedbackMessage, 
    {User}.[Name] AS Name, 
    {User}.[Username] AS Username, 
    {dBusinessOperation}.[DisplayName] AS DisplayName, 
    {User}.[Is_Active] as IsActive 
  FROM 
    {BusinessOperationExecutionLog} 
    INNER JOIN {User} ON {User}.[Id] = {BusinessOperationExecutionLog}.[UserId] 
    INNER JOIN {dBusinessOperation} ON {dBusinessOperation}.[Id] = {BusinessOperationExecutionLog}.[BusinessOperationId] 

    @InnerJoin

  WHERE 
    (@Filter_dBusinessOperationId = '' OR {BusinessOperationExecutionLog}.[BusinessOperationId] = @Filter_dBusinessOperationId)
    AND
    (@Filter_ShowOnlyUnSuccessfulOperation = 0 OR {BusinessOperationExecutionLog}.[Success] = 0)
    AND
    (
      (
        @Filter_DateFrom = @Data_NullDate 
        AND @Filter_DateTo = @Data_NullDate
      ) 
      OR 
      (
        (
          @Filter_DateFrom = @Data_NullDate 
          OR {BusinessOperationExecutionLog}.[Instant] >= @Filter_DateFrom
        ) 
        AND 
        (
          @Filter_DateTo = @Data_NullDate 
          OR {BusinessOperationExecutionLog}.[Instant] <= @Filter_DateTo
        )
      )
    ) 
    AND 
    (
      @Filter_PerformerId = '' 
      OR {User}.[Id] IN (@Filter_PerformerIdInline)
    )   
),

HistoryAndLogsCount AS (
    SELECT COUNT(*) AS CountRows
    FROM HistoryAndLogs
)

SELECT HistoryAndLogs.*, HistoryAndLogsCount.[CountRows]
FROM HistoryAndLogs, HistoryAndLogsCount
ORDER BY
    HistoryAndLogs.[Instant] DESC
OFFSET @Table_Offset ROWS
FETCH NEXT @Table_Limit ROWS ONLY;

                              
